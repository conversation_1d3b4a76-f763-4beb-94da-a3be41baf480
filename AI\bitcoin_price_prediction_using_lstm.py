# Bitcoin Price Prediction using VMD-LSTM-ELM Hybrid Model
# Enhanced training strategy based on improve_strategy.txt requirements

import os
import pandas as pd
import numpy as np
import math
import datetime as dt
import matplotlib.pyplot as plt
import joblib
import csv
import shutil
import warnings
warnings.filterwarnings('ignore')

# For evaluation and preprocessing
from sklearn.metrics import mean_squared_error, mean_absolute_error, mean_absolute_percentage_error
from sklearn.preprocessing import MinMaxScaler
from sklearn.base import BaseEstimator, RegressorMixin
from sklearn.model_selection import TimeSeriesSplit

# For model building
import tensorflow as tf
from tensorflow.keras.models import Model, Sequential
from tensorflow.keras.layers import Dense, Dropout, Input, LSTM
from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau
from tensorflow.keras.optimizers import Nadam, Adam

# For VMD decomposition - install with: pip install vmdpy
try:
    from vmdpy import VMD
    VMD_AVAILABLE = True
except ImportError:
    print("Warning: vmdpy not available. Install with: pip install vmdpy")
    VMD_AVAILABLE = False

# Set random seeds for reproducibility
np.random.seed(42)
tf.random.set_seed(42)

# Sample Entropy Implementation for VMD component classification
def sample_entropy(data, m=2, r=0.2):
    """Calculate sample entropy of a time series"""
    def _maxdist(xi, xj, m):
        return max([abs(ua - va) for ua, va in zip(xi, xj)])

    def _phi(data, m, r):
        patterns = np.array([data[i:i+m] for i in range(len(data) - m + 1)])
        N = len(patterns)

        phi = 0.0
        for i in range(N):
            template = patterns[i]
            matches = 0
            for j in range(N):
                if i != j:
                    if _maxdist(template, patterns[j], m) <= r:
                        matches += 1

            if matches > 0:
                phi += np.log(matches / (N - 1))

        return phi / N

    if isinstance(r, float) and r < 1:
        r = r * np.std(data)

    phi_m = _phi(data, m, r)
    phi_m_plus_1 = _phi(data, m + 1, r)

    return phi_m - phi_m_plus_1

# VMD Decomposition Functions
def perform_vmd_decomposition(data, K=5, alpha=2000):
    """
    Perform Variational Mode Decomposition on price data

    Args:
        data: Input time series data
        K: Number of modes (3-5 as per requirements)
        alpha: Balancing parameter

    Returns:
        imfs: Intrinsic Mode Functions
        high_freq: High-frequency component
        medium_freq: Medium-frequency component
        low_freq: Low-frequency component
    """
    if not VMD_AVAILABLE:
        print("VMD not available, using simple moving averages as fallback")
        return create_fallback_components(data)

    try:
        # VMD parameters
        tau = 0.
        DC = 0
        init = 1
        tol = 1e-7

        # Perform VMD decomposition
        imfs, _, _ = VMD(data, alpha, tau, K, DC, init, tol)

        # Reconstruct components based on sample entropy
        high_freq, medium_freq, low_freq = reconstruct_components(imfs)

        return imfs, high_freq, medium_freq, low_freq

    except Exception as e:
        print(f"VMD decomposition failed: {e}, using fallback method")
        return create_fallback_components(data)

def reconstruct_components(imfs):
    """Reconstruct components based on sample entropy"""

    # Calculate sample entropy for each IMF
    entropies = []
    for i in range(len(imfs)):
        try:
            entropy = sample_entropy(imfs[i])
            entropies.append(entropy)
        except:
            # If entropy calculation fails, use variance as proxy
            entropies.append(np.var(imfs[i]))

    # Sort IMFs by entropy
    entropy_indices = np.argsort(entropies)

    # Reconstruct components
    n_imfs = len(imfs)

    # High-frequency component (first 2 IMFs with highest entropy)
    high_freq_indices = entropy_indices[-2:] if n_imfs >= 2 else [entropy_indices[-1]]
    high_freq_component = np.sum([imfs[i] for i in high_freq_indices], axis=0)

    # Medium-frequency component (middle IMFs)
    if n_imfs >= 4:
        medium_freq_indices = entropy_indices[1:-2]
    elif n_imfs >= 3:
        medium_freq_indices = [entropy_indices[1]]
    else:
        medium_freq_indices = [entropy_indices[0]]

    medium_freq_component = np.sum([imfs[i] for i in medium_freq_indices], axis=0)

    # Low-frequency component (first 1-2 IMFs with lowest entropy)
    low_freq_indices = entropy_indices[:2] if n_imfs >= 2 else [entropy_indices[0]]
    low_freq_component = np.sum([imfs[i] for i in low_freq_indices], axis=0)

    return high_freq_component, medium_freq_component, low_freq_component

def create_fallback_components(data):
    """Create fallback components when VMD is not available"""
    # Simple moving average decomposition as fallback
    short_ma = pd.Series(data).rolling(window=5).mean().fillna(method='bfill').values
    medium_ma = pd.Series(data).rolling(window=20).mean().fillna(method='bfill').values
    long_ma = pd.Series(data).rolling(window=50).mean().fillna(method='bfill').values

    high_freq = data - short_ma
    medium_freq = short_ma - medium_ma
    low_freq = medium_ma - long_ma

    # Create dummy IMFs for consistency
    imfs = [high_freq, medium_freq, low_freq]

    return imfs, high_freq, medium_freq, low_freq

# Extreme Learning Machine Implementation
class ELM(BaseEstimator, RegressorMixin):
    """Extreme Learning Machine for regression"""

    def __init__(self, hidden_units=50, activation='tanh', random_state=None):
        self.hidden_units = hidden_units
        self.activation = activation
        self.random_state = random_state

    def _activation_function(self, x):
        if self.activation == 'tanh':
            return np.tanh(x)
        elif self.activation == 'sigmoid':
            return 1 / (1 + np.exp(-x))
        elif self.activation == 'relu':
            return np.maximum(0, x)
        else:
            return np.tanh(x)

    def fit(self, X, y):
        if self.random_state is not None:
            np.random.seed(self.random_state)

        n_samples, n_features = X.shape

        # Randomly initialize input weights and biases
        self.input_weights = np.random.normal(0, 1, (n_features, self.hidden_units))
        self.biases = np.random.normal(0, 1, (1, self.hidden_units))

        # Calculate hidden layer output
        H = self._activation_function(np.dot(X, self.input_weights) + self.biases)

        # Calculate output weights using pseudo-inverse
        self.output_weights = np.dot(np.linalg.pinv(H), y)

        return self

    def predict(self, X):
        # Calculate hidden layer output
        H = self._activation_function(np.dot(X, self.input_weights) + self.biases)

        # Calculate prediction
        y_pred = np.dot(H, self.output_weights)

        return y_pred

# Technical Indicators Implementation
def calculate_rsi(prices, window=14):
    """Calculate Relative Strength Index"""
    delta = prices.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
    rs = gain / loss
    rsi = 100 - (100 / (1 + rs))
    return rsi.fillna(50)

def calculate_macd(prices, fast=12, slow=26, signal=9):
    """Calculate MACD (Moving Average Convergence Divergence)"""
    ema_fast = prices.ewm(span=fast).mean()
    ema_slow = prices.ewm(span=slow).mean()
    macd = ema_fast - ema_slow
    macd_signal = macd.ewm(span=signal).mean()
    macd_histogram = macd - macd_signal
    return macd, macd_signal, macd_histogram

def calculate_momentum(prices, window=30):
    """Calculate Momentum"""
    momentum = prices.diff(window)
    return momentum.fillna(0)

def calculate_stochastic(high, low, close, k_window=14, d_window=3):
    """Calculate Stochastic Oscillator"""
    lowest_low = low.rolling(window=k_window).min()
    highest_high = high.rolling(window=k_window).max()
    k_percent = 100 * ((close - lowest_low) / (highest_high - lowest_low))
    d_percent = k_percent.rolling(window=d_window).mean()
    return k_percent.fillna(50), d_percent.fillna(50)

def calculate_bollinger_bands(prices, window=20, num_std=2):
    """Calculate Bollinger Bands"""
    rolling_mean = prices.rolling(window=window).mean()
    rolling_std = prices.rolling(window=window).std()
    upper_band = rolling_mean + (rolling_std * num_std)
    lower_band = rolling_mean - (rolling_std * num_std)
    return upper_band.fillna(method='bfill'), rolling_mean.fillna(method='bfill'), lower_band.fillna(method='bfill')

def calculate_atr(high, low, close, window=14):
    """Calculate Average True Range"""
    high_low = high - low
    high_close = np.abs(high - close.shift())
    low_close = np.abs(low - close.shift())
    ranges = pd.concat([high_low, high_close, low_close], axis=1)
    true_range = ranges.max(axis=1)
    atr = true_range.rolling(window=window).mean()
    return atr.fillna(method='bfill')

def calculate_cci(high, low, close, window=20):
    """Calculate Commodity Channel Index"""
    typical_price = (high + low + close) / 3
    sma = typical_price.rolling(window=window).mean()
    mean_deviation = typical_price.rolling(window=window).apply(lambda x: np.abs(x - x.mean()).mean())
    cci = (typical_price - sma) / (0.015 * mean_deviation)
    return cci.fillna(0)

def calculate_williams_r(high, low, close, window=14):
    """Calculate Williams %R"""
    highest_high = high.rolling(window=window).max()
    lowest_low = low.rolling(window=window).min()
    williams_r = -100 * ((highest_high - close) / (highest_high - lowest_low))
    return williams_r.fillna(-50)

def calculate_chaikin_money_flow(high, low, close, volume, window=20):
    """Calculate Chaikin Money Flow"""
    money_flow_multiplier = ((close - low) - (high - close)) / (high - low)
    money_flow_multiplier = money_flow_multiplier.fillna(0)
    money_flow_volume = money_flow_multiplier * volume
    cmf = money_flow_volume.rolling(window=window).sum() / volume.rolling(window=window).sum()
    return cmf.fillna(0)

def calculate_all_technical_indicators(df):
    """Calculate all technical indicators for the dataset"""
    indicators_df = df.copy()

    # RSI (14 and 30-day)
    indicators_df['RSI_14'] = calculate_rsi(df['Price'], 14)
    indicators_df['RSI_30'] = calculate_rsi(df['Price'], 30)

    # MACD
    macd, macd_signal, macd_histogram = calculate_macd(df['Price'])
    indicators_df['MACD'] = macd
    indicators_df['MACD_Signal'] = macd_signal
    indicators_df['MACD_Histogram'] = macd_histogram

    # Momentum
    indicators_df['Momentum_30'] = calculate_momentum(df['Price'], 30)

    # Stochastic Oscillator (multiple periods)
    k_30, d_30 = calculate_stochastic(df['High'], df['Low'], df['Price'], 30, 3)
    k_200, d_200 = calculate_stochastic(df['High'], df['Low'], df['Price'], 200, 3)
    indicators_df['Stoch_K30'] = k_30
    indicators_df['Stoch_D30'] = d_30
    indicators_df['Stoch_K200'] = k_200
    indicators_df['Stoch_D200'] = d_200

    # Bollinger Bands
    bb_upper, bb_middle, bb_lower = calculate_bollinger_bands(df['Price'])
    indicators_df['BB_Upper'] = bb_upper
    indicators_df['BB_Middle'] = bb_middle
    indicators_df['BB_Lower'] = bb_lower
    indicators_df['BB_Width'] = bb_upper - bb_lower

    # ATR
    indicators_df['ATR'] = calculate_atr(df['High'], df['Low'], df['Price'])

    # CCI
    indicators_df['CCI'] = calculate_cci(df['High'], df['Low'], df['Price'])

    # Williams %R
    indicators_df['Williams_R'] = calculate_williams_r(df['High'], df['Low'], df['Price'])

    # Chaikin Money Flow
    indicators_df['CMF'] = calculate_chaikin_money_flow(df['High'], df['Low'], df['Price'], df['Volume'])

    return indicators_df

# Advanced Feature Engineering Pipeline
def create_lag_features(df, columns, max_lag=30):
    """Create lagged features for specified columns"""
    feature_df = df.copy()

    for col in columns:
        for lag in range(1, max_lag + 1):
            feature_df[f'{col}_lag_{lag}'] = df[col].shift(lag)

    return feature_df

def create_volatility_features(df, windows=[5, 10, 20, 30]):
    """Create rolling volatility features"""
    feature_df = df.copy()

    # Price volatility
    for window in windows:
        feature_df[f'Price_volatility_{window}'] = df['Price'].rolling(window=window).std()
        feature_df[f'Volume_volatility_{window}'] = df['Volume'].rolling(window=window).std()

        # Price returns volatility
        returns = df['Price'].pct_change()
        feature_df[f'Returns_volatility_{window}'] = returns.rolling(window=window).std()

    return feature_df

def encode_bitcoin_halving_cycle(df):
    """Encode Bitcoin halving cycle features"""
    feature_df = df.copy()

    # Bitcoin halving dates (approximate)
    halving_dates = [
        pd.Timestamp('2012-11-28'),  # First halving
        pd.Timestamp('2016-07-09'),  # Second halving
        pd.Timestamp('2020-05-11'),  # Third halving
        pd.Timestamp('2024-04-20'),  # Fourth halving (estimated)
        pd.Timestamp('2028-04-20'),  # Fifth halving (estimated)
    ]

    # Calculate days since last halving and days to next halving
    dates = pd.to_datetime(df['Date'])

    days_since_halving = []
    days_to_halving = []
    halving_cycle_position = []

    for date in dates:
        # Find the most recent halving before this date
        past_halvings = [h for h in halving_dates if h <= date]
        future_halvings = [h for h in halving_dates if h > date]

        if past_halvings:
            last_halving = max(past_halvings)
            days_since = (date - last_halving).days
        else:
            days_since = 0

        if future_halvings:
            next_halving = min(future_halvings)
            days_to = (next_halving - date).days
        else:
            days_to = 1460  # Approximate 4 years in days

        # Cycle position (0 to 1, where 0 is just after halving, 1 is just before next halving)
        cycle_length = days_since + days_to
        if cycle_length > 0:
            position = days_since / cycle_length
        else:
            position = 0

        days_since_halving.append(days_since)
        days_to_halving.append(days_to)
        halving_cycle_position.append(position)

    feature_df['Days_Since_Halving'] = days_since_halving
    feature_df['Days_To_Halving'] = days_to_halving
    feature_df['Halving_Cycle_Position'] = halving_cycle_position

    # Cyclical encoding of halving position
    feature_df['Halving_Cycle_Sin'] = np.sin(2 * np.pi * feature_df['Halving_Cycle_Position'])
    feature_df['Halving_Cycle_Cos'] = np.cos(2 * np.pi * feature_df['Halving_Cycle_Position'])

    return feature_df

def create_time_features(df):
    """Create time-based features"""
    feature_df = df.copy()
    dates = pd.to_datetime(df['Date'])

    # Basic time features
    feature_df['Year'] = dates.dt.year
    feature_df['Month'] = dates.dt.month
    feature_df['Day'] = dates.dt.day
    feature_df['DayOfWeek'] = dates.dt.dayofweek
    feature_df['DayOfYear'] = dates.dt.dayofyear
    feature_df['Quarter'] = dates.dt.quarter

    # Cyclical encoding for seasonal patterns
    feature_df['Month_Sin'] = np.sin(2 * np.pi * feature_df['Month'] / 12)
    feature_df['Month_Cos'] = np.cos(2 * np.pi * feature_df['Month'] / 12)
    feature_df['DayOfWeek_Sin'] = np.sin(2 * np.pi * feature_df['DayOfWeek'] / 7)
    feature_df['DayOfWeek_Cos'] = np.cos(2 * np.pi * feature_df['DayOfWeek'] / 7)
    feature_df['DayOfYear_Sin'] = np.sin(2 * np.pi * feature_df['DayOfYear'] / 365)
    feature_df['DayOfYear_Cos'] = np.cos(2 * np.pi * feature_df['DayOfYear'] / 365)

    return feature_df

def create_comprehensive_features(df):
    """Create comprehensive feature set combining all feature engineering techniques"""
    print("Creating comprehensive feature set...")

    # Start with technical indicators
    feature_df = calculate_all_technical_indicators(df)
    print(f"Added technical indicators. Shape: {feature_df.shape}")

    # Add time features
    feature_df = create_time_features(feature_df)
    print(f"Added time features. Shape: {feature_df.shape}")

    # Add halving cycle features
    feature_df = encode_bitcoin_halving_cycle(feature_df)
    print(f"Added halving cycle features. Shape: {feature_df.shape}")

    # Add volatility features
    feature_df = create_volatility_features(feature_df)
    print(f"Added volatility features. Shape: {feature_df.shape}")

    # Add lag features for key indicators (limited to reduce dimensionality)
    key_columns = ['Price', 'Volume', 'RSI_14', 'MACD', 'BB_Width', 'ATR']
    feature_df = create_lag_features(feature_df, key_columns, max_lag=10)
    print(f"Added lag features. Shape: {feature_df.shape}")

    # Fill any remaining NaN values
    feature_df = feature_df.fillna(method='bfill').fillna(method='ffill')

    print(f"Final feature set shape: {feature_df.shape}")
    return feature_df

# VMD-LSTM-ELM Hybrid Model Architecture
class VMD_LSTM_ELM_Hybrid:
    """VMD-LSTM-ELM Hybrid Model for Bitcoin Price Prediction"""

    def __init__(self, vmd_k=5, vmd_alpha=2000, lstm_units=64, lstm_epochs=100,
                 elm_hidden_units=100, sequence_length=60, random_state=42):

        self.vmd_k = vmd_k
        self.vmd_alpha = vmd_alpha
        self.lstm_units = lstm_units
        self.lstm_epochs = lstm_epochs
        self.elm_hidden_units = elm_hidden_units
        self.sequence_length = sequence_length
        self.random_state = random_state

        # Initialize models
        self.lstm_model = None
        self.elm_models = []
        self.scalers = []
        self.feature_scalers = []

        # Set random seeds
        np.random.seed(random_state)
        tf.random.set_seed(random_state)

    def _create_sequences(self, data, sequence_length):
        """Create sequences for training"""
        X, y = [], []
        for i in range(len(data) - sequence_length):
            X.append(data[i:(i + sequence_length)])
            y.append(data[i + sequence_length])
        return np.array(X), np.array(y)

    def _build_lstm_model(self, input_shape):
        """Build enhanced LSTM model for high-frequency component"""
        model = Sequential([
            LSTM(self.lstm_units, return_sequences=True, input_shape=input_shape),
            Dropout(0.2),
            LSTM(self.lstm_units, return_sequences=True),
            Dropout(0.2),
            LSTM(self.lstm_units, return_sequences=False),
            Dropout(0.2),
            Dense(32, activation='relu'),
            Dense(1)
        ])

        model.compile(optimizer=Adam(learning_rate=0.001), loss='mse', metrics=['mae'])
        return model

    def fit(self, price_data, feature_data=None):
        """
        Fit the VMD-LSTM-ELM hybrid model

        Args:
            price_data: Price time series data
            feature_data: Additional feature data (optional)
        """
        print("Starting VMD-LSTM-ELM hybrid model training...")

        # Step 1: VMD decomposition
        print("Performing VMD decomposition...")
        imfs, high_freq, medium_freq, low_freq = perform_vmd_decomposition(
            price_data, K=self.vmd_k, alpha=self.vmd_alpha
        )

        print(f"VMD decomposition complete. Generated {len(imfs)} components.")

        # Step 2: Train LSTM for high-frequency component
        print("Training LSTM for high-frequency component...")

        scaler_high = MinMaxScaler()
        high_freq_scaled = scaler_high.fit_transform(high_freq.reshape(-1, 1)).flatten()
        self.scalers.append(scaler_high)

        # Create sequences for LSTM
        X_lstm, y_lstm = self._create_sequences(high_freq_scaled, self.sequence_length)

        # Add feature data if available
        if feature_data is not None:
            # Scale feature data
            feature_scaler = MinMaxScaler()
            feature_scaled = feature_scaler.fit_transform(feature_data)
            self.feature_scalers.append(feature_scaler)

            # Create feature sequences
            feature_sequences = []
            for i in range(len(feature_scaled) - self.sequence_length):
                feature_sequences.append(feature_scaled[i:(i + self.sequence_length)])
            feature_sequences = np.array(feature_sequences)

            # Combine price and feature data
            if len(feature_sequences) == len(X_lstm):
                X_lstm = X_lstm.reshape((X_lstm.shape[0], X_lstm.shape[1], 1))
                X_lstm = np.concatenate([X_lstm, feature_sequences], axis=2)
            else:
                X_lstm = X_lstm.reshape((X_lstm.shape[0], X_lstm.shape[1], 1))
        else:
            X_lstm = X_lstm.reshape((X_lstm.shape[0], X_lstm.shape[1], 1))

        # Build and train LSTM
        self.lstm_model = self._build_lstm_model((self.sequence_length, X_lstm.shape[2]))

        early_stop = EarlyStopping(monitor='loss', patience=15, restore_best_weights=True)
        reduce_lr = ReduceLROnPlateau(monitor='loss', factor=0.5, patience=10, min_lr=1e-7)

        self.lstm_model.fit(
            X_lstm, y_lstm,
            epochs=self.lstm_epochs,
            batch_size=32,
            verbose=1,
            callbacks=[early_stop, reduce_lr]
        )

        print("LSTM training complete.")

        # Step 3: Train ELM for medium and low frequency components
        print("Training ELM for medium and low frequency components...")

        self.elm_models = []

        for i, component in enumerate([medium_freq, low_freq]):
            scaler = MinMaxScaler()
            component_scaled = scaler.fit_transform(component.reshape(-1, 1)).flatten()
            self.scalers.append(scaler)

            # Create sequences for ELM
            X_elm, y_elm = self._create_sequences(component_scaled, self.sequence_length)
            X_elm = X_elm.reshape(X_elm.shape[0], -1)  # Flatten for ELM

            # Add feature data for ELM if available
            if feature_data is not None and len(self.feature_scalers) > 0:
                feature_sequences_elm = []
                for j in range(len(feature_scaled) - self.sequence_length):
                    feature_sequences_elm.append(feature_scaled[j:(j + self.sequence_length)].flatten())
                feature_sequences_elm = np.array(feature_sequences_elm)

                if len(feature_sequences_elm) == len(X_elm):
                    X_elm = np.concatenate([X_elm, feature_sequences_elm], axis=1)

            # Train ELM
            elm_model = ELM(hidden_units=self.elm_hidden_units, random_state=self.random_state)
            elm_model.fit(X_elm, y_elm)
            self.elm_models.append(elm_model)

        print("ELM training complete.")
        print("VMD-LSTM-ELM hybrid model training finished!")

        return self

    def predict(self, price_data, feature_data=None):
        """Predict using the VMD-LSTM-ELM hybrid model"""

        # Step 1: VMD decomposition
        imfs, high_freq, medium_freq, low_freq = perform_vmd_decomposition(
            price_data, K=self.vmd_k, alpha=self.vmd_alpha
        )

        # Step 2: Make predictions for each component
        predictions = []

        # High-frequency prediction using LSTM
        high_freq_scaled = self.scalers[0].transform(high_freq.reshape(-1, 1)).flatten()
        X_lstm, _ = self._create_sequences(high_freq_scaled, self.sequence_length)

        # Add feature data if available
        if feature_data is not None and len(self.feature_scalers) > 0:
            feature_scaled = self.feature_scalers[0].transform(feature_data)
            feature_sequences = []
            for i in range(len(feature_scaled) - self.sequence_length):
                feature_sequences.append(feature_scaled[i:(i + self.sequence_length)])
            feature_sequences = np.array(feature_sequences)

            if len(feature_sequences) == len(X_lstm):
                X_lstm = X_lstm.reshape((X_lstm.shape[0], X_lstm.shape[1], 1))
                X_lstm = np.concatenate([X_lstm, feature_sequences], axis=2)
            else:
                X_lstm = X_lstm.reshape((X_lstm.shape[0], X_lstm.shape[1], 1))
        else:
            X_lstm = X_lstm.reshape((X_lstm.shape[0], X_lstm.shape[1], 1))

        pred_high = self.lstm_model.predict(X_lstm, verbose=0)
        pred_high = self.scalers[0].inverse_transform(pred_high)
        predictions.append(pred_high.flatten())

        # Medium and low frequency predictions using ELM
        for i, component in enumerate([medium_freq, low_freq]):
            component_scaled = self.scalers[i+1].transform(component.reshape(-1, 1)).flatten()
            X_elm, _ = self._create_sequences(component_scaled, self.sequence_length)
            X_elm = X_elm.reshape(X_elm.shape[0], -1)

            # Add feature data for ELM if available
            if feature_data is not None and len(self.feature_scalers) > 0:
                feature_scaled = self.feature_scalers[0].transform(feature_data)
                feature_sequences_elm = []
                for j in range(len(feature_scaled) - self.sequence_length):
                    feature_sequences_elm.append(feature_scaled[j:(j + self.sequence_length)].flatten())
                feature_sequences_elm = np.array(feature_sequences_elm)

                if len(feature_sequences_elm) == len(X_elm):
                    X_elm = np.concatenate([X_elm, feature_sequences_elm], axis=1)

            pred_comp = self.elm_models[i].predict(X_elm)
            pred_comp = self.scalers[i+1].inverse_transform(pred_comp.reshape(-1, 1))
            predictions.append(pred_comp.flatten())

        # Step 3: Combine predictions
        min_length = min(len(pred) for pred in predictions)
        ensemble_prediction = np.sum([pred[:min_length] for pred in predictions], axis=0)

        return ensemble_prediction

# Enhanced Training Strategy Functions
def create_time_series_splits(data_length, train_ratio=0.7, val_ratio=0.15, test_ratio=0.15):
    """
    Create time series splits for training, validation, and testing

    Args:
        data_length: Total length of the dataset
        train_ratio: Proportion for training (default 0.7)
        val_ratio: Proportion for validation (default 0.15)
        test_ratio: Proportion for testing (default 0.15)

    Returns:
        train_idx, val_idx, test_idx: Indices for each split
    """
    train_size = int(data_length * train_ratio)
    val_size = int(data_length * val_ratio)

    train_idx = list(range(0, train_size))
    val_idx = list(range(train_size, train_size + val_size))
    test_idx = list(range(train_size + val_size, data_length))

    return train_idx, val_idx, test_idx

def expanding_window_validation(model_class, data, target, window_size, n_splits=5):
    """
    Perform expanding window validation for time series

    Args:
        model_class: Model class to instantiate
        data: Feature data
        target: Target data
        window_size: Window size for sequences
        n_splits: Number of validation splits

    Returns:
        validation_scores: List of validation scores
    """
    validation_scores = []
    data_length = len(data)

    # Calculate split points for expanding window
    min_train_size = int(data_length * 0.5)  # Minimum 50% for initial training
    split_points = np.linspace(min_train_size, int(data_length * 0.8), n_splits).astype(int)

    for i, split_point in enumerate(split_points):
        print(f"Expanding window validation {i+1}/{n_splits} - Training size: {split_point}")

        # Split data
        train_data = data[:split_point]
        train_target = target[:split_point]
        val_data = data[split_point:split_point + int(data_length * 0.1)]
        val_target = target[split_point:split_point + int(data_length * 0.1)]

        if len(val_data) < window_size:
            continue

        try:
            # Train model
            model = model_class()
            model.fit(train_data, train_target)

            # Validate
            predictions = model.predict(val_data)

            # Calculate score (RMSE)
            min_length = min(len(predictions), len(val_target))
            score = np.sqrt(mean_squared_error(
                val_target[:min_length],
                predictions[:min_length]
            ))
            validation_scores.append(score)

        except Exception as e:
            print(f"Validation split {i+1} failed: {e}")
            continue

    return validation_scores

def time_series_cross_validation(model_class, data, target, window_size, n_splits=5):
    """
    Perform time series cross-validation

    Args:
        model_class: Model class to instantiate
        data: Feature data
        target: Target data
        window_size: Window size for sequences
        n_splits: Number of CV splits

    Returns:
        cv_scores: Cross-validation scores
    """
    tscv = TimeSeriesSplit(n_splits=n_splits)
    cv_scores = []

    for i, (train_idx, val_idx) in enumerate(tscv.split(data)):
        print(f"Time series CV fold {i+1}/{n_splits}")

        train_data = data[train_idx]
        train_target = target[train_idx]
        val_data = data[val_idx]
        val_target = target[val_idx]

        if len(val_data) < window_size:
            continue

        try:
            # Train model
            model = model_class()
            model.fit(train_data, train_target)

            # Validate
            predictions = model.predict(val_data)

            # Calculate score (RMSE)
            min_length = min(len(predictions), len(val_target))
            score = np.sqrt(mean_squared_error(
                val_target[:min_length],
                predictions[:min_length]
            ))
            cv_scores.append(score)

        except Exception as e:
            print(f"CV fold {i+1} failed: {e}")
            continue

    return cv_scores

# Meta-Learner Neural Network for Ensemble Weights
class MetaLearnerNN:
    """Neural Network Meta-Learner for optimal ensemble weight determination"""

    def __init__(self, n_base_models=3, hidden_units=32, random_state=42):
        self.n_base_models = n_base_models
        self.hidden_units = hidden_units
        self.random_state = random_state
        self.model = None
        self.scaler = MinMaxScaler()

        # Set random seed
        np.random.seed(random_state)
        tf.random.set_seed(random_state)

    def _build_meta_model(self):
        """Build meta-learner neural network"""
        model = Sequential([
            Dense(self.hidden_units, activation='relu', input_shape=(self.n_base_models,)),
            Dropout(0.2),
            Dense(self.hidden_units // 2, activation='relu'),
            Dropout(0.2),
            Dense(1, activation='linear')
        ])

        model.compile(optimizer=Adam(learning_rate=0.001), loss='mse', metrics=['mae'])
        return model

    def fit(self, base_predictions, true_values):
        """
        Train meta-learner on base model predictions

        Args:
            base_predictions: Array of shape (n_samples, n_base_models)
            true_values: Array of true target values
        """
        # Scale base predictions
        base_predictions_scaled = self.scaler.fit_transform(base_predictions)

        # Build and train meta-model
        self.model = self._build_meta_model()

        early_stop = EarlyStopping(monitor='loss', patience=10, restore_best_weights=True)

        self.model.fit(
            base_predictions_scaled, true_values,
            epochs=100,
            batch_size=32,
            verbose=0,
            callbacks=[early_stop]
        )

        return self

    def predict(self, base_predictions):
        """Generate ensemble prediction using meta-learner"""
        base_predictions_scaled = self.scaler.transform(base_predictions)
        return self.model.predict(base_predictions_scaled, verbose=0)

# Bayesian Neural Network for Confidence Intervals
class BayesianNN:
    """Bayesian Neural Network for uncertainty quantification"""

    def __init__(self, input_dim, hidden_units=64, n_samples=100, random_state=42):
        self.input_dim = input_dim
        self.hidden_units = hidden_units
        self.n_samples = n_samples
        self.random_state = random_state
        self.models = []

        # Set random seed
        np.random.seed(random_state)
        tf.random.set_seed(random_state)

    def _build_bayesian_model(self):
        """Build a single model for the ensemble"""
        model = Sequential([
            Dense(self.hidden_units, activation='relu', input_shape=(self.input_dim,)),
            Dropout(0.3),  # Higher dropout for uncertainty
            Dense(self.hidden_units // 2, activation='relu'),
            Dropout(0.3),
            Dense(1, activation='linear')
        ])

        model.compile(optimizer=Adam(learning_rate=0.001), loss='mse')
        return model

    def fit(self, X, y, n_models=10):
        """
        Train ensemble of models for Bayesian approximation

        Args:
            X: Input features
            y: Target values
            n_models: Number of models in ensemble
        """
        self.models = []

        for i in range(n_models):
            print(f"Training Bayesian model {i+1}/{n_models}")

            # Add noise to training data for diversity
            X_noisy = X + np.random.normal(0, 0.01, X.shape)

            model = self._build_bayesian_model()

            early_stop = EarlyStopping(monitor='loss', patience=10, restore_best_weights=True)

            model.fit(
                X_noisy, y,
                epochs=50,
                batch_size=32,
                verbose=0,
                callbacks=[early_stop]
            )

            self.models.append(model)

        return self

    def predict_with_uncertainty(self, X):
        """
        Generate predictions with uncertainty estimates

        Args:
            X: Input features

        Returns:
            mean_pred: Mean prediction
            std_pred: Standard deviation (uncertainty)
            predictions: All individual predictions
        """
        predictions = []

        for model in self.models:
            # Multiple forward passes with dropout for uncertainty
            model_preds = []
            for _ in range(self.n_samples // len(self.models)):
                pred = model.predict(X, verbose=0)
                model_preds.append(pred.flatten())

            predictions.extend(model_preds)

        predictions = np.array(predictions)

        mean_pred = np.mean(predictions, axis=0)
        std_pred = np.std(predictions, axis=0)

        return mean_pred, std_pred, predictions

# Enhanced Ensemble Model with Meta-Learning
class EnhancedEnsembleModel:
    """Enhanced ensemble model combining VMD-LSTM-ELM with meta-learning"""

    def __init__(self, vmd_k=5, vmd_alpha=2000, lstm_units=64, elm_hidden_units=100,
                 sequence_length=60, random_state=42):

        self.vmd_k = vmd_k
        self.vmd_alpha = vmd_alpha
        self.lstm_units = lstm_units
        self.elm_hidden_units = elm_hidden_units
        self.sequence_length = sequence_length
        self.random_state = random_state

        # Initialize components
        self.vmd_lstm_elm = VMD_LSTM_ELM_Hybrid(
            vmd_k=vmd_k, vmd_alpha=vmd_alpha, lstm_units=lstm_units,
            elm_hidden_units=elm_hidden_units, sequence_length=sequence_length,
            random_state=random_state
        )

        self.meta_learner = None
        self.bayesian_nn = None
        self.price_scaler = MinMaxScaler()

        # Set random seed
        np.random.seed(random_state)
        tf.random.set_seed(random_state)

    def fit(self, price_data, feature_data=None):
        """Train the enhanced ensemble model"""
        print("Training Enhanced Ensemble Model...")

        # Scale price data
        price_scaled = self.price_scaler.fit_transform(price_data.reshape(-1, 1)).flatten()

        # Train VMD-LSTM-ELM hybrid model
        self.vmd_lstm_elm.fit(price_scaled, feature_data)

        # Generate base predictions for meta-learning
        base_predictions = self.vmd_lstm_elm.predict(price_scaled, feature_data)

        # Prepare data for meta-learner (using simple features for demonstration)
        if len(base_predictions) > self.sequence_length:
            # Create simple ensemble features
            ensemble_features = np.column_stack([
                base_predictions,
                np.roll(base_predictions, 1),  # Lagged prediction
                np.roll(base_predictions, 2)   # Double lagged prediction
            ])[2:]  # Remove first 2 rows due to lagging

            target_values = price_scaled[self.sequence_length+2:]  # Align with features

            if len(ensemble_features) == len(target_values) and len(ensemble_features) > 0:
                # Train meta-learner
                self.meta_learner = MetaLearnerNN(n_base_models=3, random_state=self.random_state)
                self.meta_learner.fit(ensemble_features, target_values)

                # Train Bayesian NN for uncertainty
                if feature_data is not None:
                    feature_dim = feature_data.shape[1] if len(feature_data.shape) > 1 else 1
                    self.bayesian_nn = BayesianNN(input_dim=feature_dim, random_state=self.random_state)

                    # Prepare feature sequences for Bayesian NN
                    feature_sequences = []
                    for i in range(len(feature_data) - self.sequence_length):
                        feature_sequences.append(feature_data[i:i+self.sequence_length].flatten())
                    feature_sequences = np.array(feature_sequences)

                    if len(feature_sequences) > 0:
                        target_for_bayesian = price_scaled[self.sequence_length:]
                        min_len = min(len(feature_sequences), len(target_for_bayesian))

                        self.bayesian_nn.fit(
                            feature_sequences[:min_len],
                            target_for_bayesian[:min_len]
                        )

        print("Enhanced Ensemble Model training complete!")
        return self

    def predict_with_confidence(self, price_data, feature_data=None):
        """Generate predictions with confidence intervals"""

        # Scale price data
        price_scaled = self.price_scaler.transform(price_data.reshape(-1, 1)).flatten()

        # Get base predictions
        base_predictions = self.vmd_lstm_elm.predict(price_scaled, feature_data)

        # Meta-learner prediction if available
        if self.meta_learner is not None and len(base_predictions) > 2:
            ensemble_features = np.column_stack([
                base_predictions,
                np.roll(base_predictions, 1),
                np.roll(base_predictions, 2)
            ])[2:]

            meta_predictions = self.meta_learner.predict(ensemble_features)
            final_predictions = meta_predictions.flatten()
        else:
            final_predictions = base_predictions

        # Bayesian uncertainty if available
        confidence_intervals = None
        if self.bayesian_nn is not None and feature_data is not None:
            feature_sequences = []
            for i in range(len(feature_data) - self.sequence_length):
                feature_sequences.append(feature_data[i:i+self.sequence_length].flatten())
            feature_sequences = np.array(feature_sequences)

            if len(feature_sequences) > 0:
                mean_pred, std_pred, _ = self.bayesian_nn.predict_with_uncertainty(feature_sequences)

                # Create confidence intervals (95%)
                confidence_intervals = {
                    'lower': mean_pred - 1.96 * std_pred,
                    'upper': mean_pred + 1.96 * std_pred,
                    'std': std_pred
                }

        # Transform back to original scale
        final_predictions_original = self.price_scaler.inverse_transform(
            final_predictions.reshape(-1, 1)
        ).flatten()

        return final_predictions_original, confidence_intervals

"""# 3. Loading Dataset"""

# Function to clean up the price values (remove commas and quotes)
def clean_price(price_str):
    if isinstance(price_str, str):
        return float(price_str.replace('"', '').replace(',', ''))
    return price_str

# Function to process volume data with K, M, B suffixes
def process_volume(vol_str):
    if isinstance(vol_str, str):
        if 'K' in vol_str:
            return float(vol_str.replace('K', '')) * 1000
        elif 'M' in vol_str:
            return float(vol_str.replace('M', '')) * 1000000
        elif 'B' in vol_str:
            return float(vol_str.replace('B', '')) * 1000000000
        else:
            return float(vol_str)
    return vol_str

# Function to load and preprocess data
def load_data():
    # Load our dataset
    maindf = pd.read_csv('Data/Bitcoin Historical Data.csv')

    # Clean numeric columns - they have commas and quotes
    numeric_columns = ['Price', 'Open', 'High', 'Low']
    for col in numeric_columns:
        maindf[col] = maindf[col].apply(clean_price)

    # Handle the 'Vol.' column
    maindf['Volume'] = maindf['Vol.'].apply(process_volume)

    # Convert Date to datetime
    maindf['Date'] = pd.to_datetime(maindf['Date'], format='%m/%d/%Y')

    # Since the data is in reverse chronological order (newest first), sort it chronologically
    maindf = maindf.sort_values('Date')

    # Remove any rows with NaN values
    maindf = maindf.dropna()

    # Check for any infinite values and replace with NaN, then drop
    maindf = maindf.replace([np.inf, -np.inf], np.nan).dropna()

    print('Total number of days present in the dataset: ', maindf.shape[0])
    print('Total number of fields present in the dataset: ', maindf.shape[1])

    # Check for any remaining NaN or infinite values
    print('NaN values in Price:', maindf['Price'].isna().sum())
    print('NaN values in Volume:', maindf['Volume'].isna().sum())
    print('Infinite values in Price:', np.isinf(maindf['Price']).sum())
    print('Infinite values in Volume:', np.isinf(maindf['Volume']).sum())

    return maindf

# Advanced Multivariate LSTM model architecture
def build_advanced_lstm_model(window_size, n_features=2):
    """Build advanced LSTM model with functional API supporting multivariate input"""
    input1 = Input(shape=(window_size, n_features))
    x = LSTM(units=64, return_sequences=True)(input1)
    x = Dropout(0.2)(x)
    x = LSTM(units=64, return_sequences=True)(x)
    x = Dropout(0.2)(x)
    x = LSTM(units=64)(x)
    x = Dropout(0.2)(x)
    x = Dense(32, activation='relu')(x)
    dnn_output = Dense(1)(x)
    model = Model(inputs=input1, outputs=[dnn_output])
    return model

def evaluate_model(model, X, y, scaler, name=""):
    """Evaluate model performance with various metrics"""
    predictions = model.predict(X)

    # Transform back to original form
    predictions = scaler.inverse_transform(predictions)
    original_y = scaler.inverse_transform(y.reshape(-1,1))

    # Calculate metrics
    rmse = math.sqrt(mean_squared_error(original_y, predictions))
    mae = mean_absolute_error(original_y, predictions)

    print(f"\n{name} Evaluation Metrics:")
    print(f"RMSE: {rmse:.2f}")
    print(f"MAE: {mae:.2f}")

    return rmse, mae

def create_multivariate_sequences(price_data, volume_data, window_size):
    """Create sequences with both price and volume features"""
    X, y = [], []
    for i in range(window_size, len(price_data)):
        # Combine price and volume features
        price_seq = price_data[i-window_size:i, 0]
        volume_seq = volume_data[i-window_size:i, 0]

        # Check for NaN or infinite values
        if np.any(np.isnan(price_seq)) or np.any(np.isnan(volume_seq)) or \
           np.any(np.isinf(price_seq)) or np.any(np.isinf(volume_seq)) or \
           np.isnan(price_data[i, 0]) or np.isinf(price_data[i, 0]):
            continue

        # Stack features: [price_seq, volume_seq]
        feature_seq = np.column_stack([price_seq, volume_seq])
        X.append(feature_seq)
        y.append(price_data[i, 0])

    X = np.array(X)
    y = np.array(y)

    # Final check for NaN values
    if np.any(np.isnan(X)) or np.any(np.isnan(y)):
        print("Warning: NaN values found in sequences, removing them...")
        valid_indices = ~(np.any(np.isnan(X.reshape(X.shape[0], -1)), axis=1) | np.isnan(y))
        X = X[valid_indices]
        y = y[valid_indices]

    return X, y

def predict_future_multivariate(model, price_scaler, volume_scaler, price_data, volume_data, window_size, days_to_predict):
    """
    Generate future price predictions using the trained multivariate LSTM model

    Args:
        model: Trained LSTM model
        price_scaler: Fitted MinMaxScaler for price data
        volume_scaler: Fitted MinMaxScaler for volume data
        price_data: Scaled price data array
        volume_data: Scaled volume data array
        window_size: Number of days to look back for prediction
        days_to_predict: Number of future days to predict

    Returns:
        predictions: Array of predicted prices in original scale
    """
    print(f"\n=== Generating Future Predictions for {days_to_predict} Days ===")

    # Get the last window_size days of data for initial input
    last_price_sequence = price_data[-window_size:].flatten()
    last_volume_sequence = volume_data[-window_size:].flatten()

    # Initialize input sequences
    temp_input_price = list(last_price_sequence)
    temp_input_volume = list(last_volume_sequence)

    # Calculate historical volatility for noise generation
    historical_data = price_scaler.inverse_transform(price_data[-30:])  # Use last 30 days
    daily_returns = np.diff(historical_data.flatten()) / historical_data[:-1].flatten()
    historical_volatility = np.std(daily_returns)

    # Volatility scale factor (can be adjusted)
    volatility_scale = 0.7

    lst_output = []
    n_steps = window_size
    i = 0

    print("Generating predictions with progress tracking...")
    print(f"Initial sequence lengths - Price: {len(temp_input_price)}, Volume: {len(temp_input_volume)}")

    while i < days_to_predict:
        if len(temp_input_price) > n_steps:
            # Use the last n_steps for prediction
            price_seq = np.array(temp_input_price[-n_steps:])
            volume_seq = np.array(temp_input_volume[-n_steps:])
        else:
            # Use all available data if less than n_steps
            price_seq = np.array(temp_input_price)
            volume_seq = np.array(temp_input_volume)

        # Ensure we have exactly n_steps
        if len(price_seq) != n_steps:
            print(f"❌ Error: Sequence length mismatch. Expected {n_steps}, got {len(price_seq)}")
            break

        # Combine features for multivariate input
        x_input = np.column_stack([price_seq, volume_seq])
        x_input = x_input.reshape((1, n_steps, 2))

        # Make prediction
        yhat = model.predict(x_input, verbose=0)
        pred_value = yhat[0][0]

        # Add controlled noise based on historical volatility
        scaled_value = pred_value
        noise = np.random.normal(0, historical_volatility * volatility_scale * abs(scaled_value), 1)[0]
        final_pred = scaled_value + noise

        # Update input sequences (remove oldest, add newest)
        temp_input_price.append(final_pred)
        temp_input_volume.append(temp_input_volume[-1])  # Use last volume value

        lst_output.append(final_pred)
        i += 1

        # Progress tracking
        if i % 100 == 0 or i == days_to_predict:
            print(f"Progress: {i}/{days_to_predict} predictions generated ({i/days_to_predict*100:.1f}%)")

    if len(lst_output) != days_to_predict:
        print(f"⚠️  Warning: Generated {len(lst_output)} predictions instead of {days_to_predict}")

    # Transform predictions back to original scale
    predictions = price_scaler.inverse_transform(np.array(lst_output).reshape(-1, 1)).reshape(1, -1).tolist()[0]

    print(f"Successfully generated {len(predictions)} predictions")
    print(f"Price range: ${min(predictions):.2f} - ${max(predictions):.2f}")

    return predictions

def generate_and_save_csv_predictions(model, price_scaler, volume_scaler, closedf, window_size):
    """
    Generate 1095-day predictions and save to CSV file

    Args:
        model: Trained LSTM model
        price_scaler: Fitted price scaler
        volume_scaler: Fitted volume scaler
        closedf: DataFrame with historical data
        window_size: Window size used for training
    """
    try:
        print("\n" + "="*80)
        print("GENERATING CSV PREDICTIONS")
        print("="*80)

        # Prepare data for prediction
        price = closedf['Price']
        volume = closedf['Volume']

        print(f"Data shapes before scaling:")
        print(f"  Price: {price.shape}")
        print(f"  Volume: {volume.shape}")

        # Scale the data using the fitted scalers
        price_scaled = price_scaler.transform(price.values.reshape(-1, 1))
        volume_scaled = volume_scaler.transform(volume.values.reshape(-1, 1))

        print(f"Data shapes after scaling:")
        print(f"  Price scaled: {price_scaled.shape}")
        print(f"  Volume scaled: {volume_scaled.shape}")
        print(f"  Window size: {window_size}")

        # Generate predictions for 1095 days (3 years)
        days_to_predict = 1095
        predictions = predict_future_multivariate(
            model, price_scaler, volume_scaler,
            price_scaled, volume_scaled,
            window_size, days_to_predict
        )

        # Generate future dates starting from the day after the last date in dataset
        last_date = closedf['Date'].max()
        future_dates = []

        print(f"\nGenerating dates starting from: {last_date.date()}")

        for i in range(1, days_to_predict + 1):
            future_date = last_date + pd.Timedelta(days=i)
            future_dates.append(future_date.strftime('%Y-%m-%d'))

        # Validate data integrity
        if len(future_dates) != len(predictions):
            raise ValueError(f"Date count ({len(future_dates)}) doesn't match prediction count ({len(predictions)})")

        if len(predictions) != days_to_predict:
            raise ValueError(f"Expected {days_to_predict} predictions, got {len(predictions)}")

        # Create CSV file path
        csv_path = 'Data/bitcoin_predictions.csv'

        # Ensure Data directory exists
        os.makedirs('Data', exist_ok=True)

        # Write to CSV file
        print(f"\nSaving predictions to: {csv_path}")

        with open(csv_path, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)

            # Write header
            writer.writerow(['Date', 'Predicted_Price'])

            # Write data rows
            for date, price in zip(future_dates, predictions):
                writer.writerow([date, f"{price:.2f}"])

        # Validation: Read back and verify
        print("\nValidating CSV file...")
        validation_df = pd.read_csv(csv_path)

        if len(validation_df) != days_to_predict:
            raise ValueError(f"CSV validation failed: Expected {days_to_predict} rows, found {len(validation_df)}")

        # Check for any missing or invalid data
        if validation_df['Date'].isnull().any():
            raise ValueError("CSV validation failed: Found null dates")

        if validation_df['Predicted_Price'].isnull().any():
            raise ValueError("CSV validation failed: Found null prices")

        # Display summary statistics
        print(f"\n=== CSV Generation Summary ===")
        print(f"File saved: {csv_path}")
        print(f"Total predictions: {len(validation_df)}")
        print(f"Date range: {validation_df['Date'].iloc[0]} to {validation_df['Date'].iloc[-1]}")
        print(f"Price range: ${validation_df['Predicted_Price'].min():.2f} - ${validation_df['Predicted_Price'].max():.2f}")
        print(f"Average predicted price: ${validation_df['Predicted_Price'].mean():.2f}")

        print("\n✓ CSV file generated and validated successfully!")

        # Copy CSV to frontend public directory
        copy_to_frontend_public(csv_path)

        return csv_path

    except Exception as e:
        print(f"\n❌ Error generating CSV predictions: {str(e)}")
        print("Please check the model and data integrity.")
        raise

def copy_to_frontend_public(csv_path):
    """Copy the CSV file to the frontend public directory for web access"""
    try:
        frontend_public_dir = 'Frontend/public/Data'
        frontend_csv_path = os.path.join(frontend_public_dir, 'bitcoin_predictions.csv')

        # Create directory if it doesn't exist
        os.makedirs(frontend_public_dir, exist_ok=True)

        # Copy the file
        shutil.copy2(csv_path, frontend_csv_path)

        # Verify copy
        if os.path.exists(frontend_csv_path):
            print(f"✅ CSV copied to frontend: {frontend_csv_path}")
        else:
            print(f"⚠️  Warning: Failed to copy CSV to frontend directory")

    except Exception as e:
        print(f"⚠️  Warning: Could not copy CSV to frontend directory: {str(e)}")
        print("You can manually copy the file or run: python copy_csv_to_public.py")

def train_and_save_model():
    """Train Enhanced VMD-LSTM-ELM Hybrid model with comprehensive feature engineering"""

    # Load the data
    maindf = load_data()

    # Prepare comprehensive DataFrame with all OHLCV data
    closedf = maindf[['Date', 'Price', 'Open', 'High', 'Low', 'Volume']].copy()
    closedf['Date'] = pd.to_datetime(closedf['Date'])
    closedf = closedf.sort_values(by='Date', ascending=True).reset_index(drop=True)

    # Ensure all columns are float64
    numeric_columns = ['Price', 'Open', 'High', 'Low', 'Volume']
    for col in numeric_columns:
        closedf[col] = closedf[col].astype('float64')

    dates = closedf['Date']

    print(f"Loaded data shape: {closedf.shape}")
    print(f"Date range: {dates.min()} to {dates.max()}")

    # Create comprehensive feature set
    print("\n=== Feature Engineering Phase ===")
    feature_df = create_comprehensive_features(closedf)

    # Remove non-numeric columns for model training
    feature_columns = [col for col in feature_df.columns if col not in ['Date']]
    feature_data = feature_df[feature_columns].values

    print(f"Feature matrix shape: {feature_data.shape}")
    print(f"Features: {len(feature_columns)} total features")

    # Enhanced Training Strategy Implementation
    print("\n=== Enhanced Training Strategy (70/15/15 Split) ===")

    # Get price data for training
    price_data = closedf['Price'].values

    # Create time series splits (70% train, 15% val, 15% test)
    train_idx, val_idx, test_idx = create_time_series_splits(len(price_data))

    print(f"Training samples: {len(train_idx)} ({len(train_idx)/len(price_data)*100:.1f}%)")
    print(f"Validation samples: {len(val_idx)} ({len(val_idx)/len(price_data)*100:.1f}%)")
    print(f"Test samples: {len(test_idx)} ({len(test_idx)/len(price_data)*100:.1f}%)")

    # Split data according to strategy
    train_price = price_data[train_idx]
    val_price = price_data[val_idx]
    test_price = price_data[test_idx]

    train_features = feature_data[train_idx] if feature_data is not None else None
    val_features = feature_data[val_idx] if feature_data is not None else None
    test_features = feature_data[test_idx] if feature_data is not None else None

    # Train Enhanced Ensemble Model
    print("\n=== Training Enhanced VMD-LSTM-ELM Ensemble Model ===")

    # Initialize enhanced ensemble model
    enhanced_model = EnhancedEnsembleModel(
        vmd_k=5,
        vmd_alpha=2000,
        lstm_units=64,
        elm_hidden_units=100,
        sequence_length=60,
        random_state=42
    )

    # Train the model
    enhanced_model.fit(train_price, train_features)

    # Validate the model
    print("\n=== Model Validation ===")
    val_predictions, val_confidence = enhanced_model.predict_with_confidence(val_price, val_features)

    # Calculate validation metrics
    min_len = min(len(val_predictions), len(val_price))
    val_rmse = np.sqrt(mean_squared_error(val_price[:min_len], val_predictions[:min_len]))
    val_mae = mean_absolute_error(val_price[:min_len], val_predictions[:min_len])
    val_mape = np.mean(np.abs((val_price[:min_len] - val_predictions[:min_len]) / val_price[:min_len])) * 100

    print(f"Validation RMSE: ${val_rmse:.2f}")
    print(f"Validation MAE: ${val_mae:.2f}")
    print(f"Validation MAPE: {val_mape:.2f}%")

    # Test the model
    print("\n=== Model Testing ===")
    test_predictions, test_confidence = enhanced_model.predict_with_confidence(test_price, test_features)

    # Calculate test metrics
    min_len = min(len(test_predictions), len(test_price))
    test_rmse = np.sqrt(mean_squared_error(test_price[:min_len], test_predictions[:min_len]))
    test_mae = mean_absolute_error(test_price[:min_len], test_predictions[:min_len])
    test_mape = np.mean(np.abs((test_price[:min_len] - test_predictions[:min_len]) / test_price[:min_len])) * 100

    print(f"Test RMSE: ${test_rmse:.2f}")
    print(f"Test MAE: ${test_mae:.2f}")
    print(f"Test MAPE: {test_mape:.2f}%")

    # For compatibility with existing CSV generation, create a simple LSTM model
    print("\n=== Creating Compatibility Model for CSV Generation ===")

    # Prepare simple price and volume data for compatibility
    price = closedf['Price']
    volume = closedf['Volume']

    # Separate scalers for price and volume (for compatibility)
    price_scaler = MinMaxScaler()
    volume_scaler = MinMaxScaler()

    price_scaler.fit(price.values.reshape(-1,1))
    volume_scaler.fit(volume.values.reshape(-1,1))

    window_size = 60

    # Create simple compatibility model
    full_price_scaled = price_scaler.transform(price.values.reshape(-1,1))
    full_volume_scaled = volume_scaler.transform(volume.values.reshape(-1,1))

    # Use 80/20 split for compatibility model
    split_idx = int(len(full_price_scaled) * 0.8)

    train_price_compat = full_price_scaled[:split_idx]
    train_volume_compat = full_volume_scaled[:split_idx]
    test_price_compat = full_price_scaled[split_idx-window_size:]
    test_volume_compat = full_volume_scaled[split_idx-window_size:]

    # Create sequences for compatibility
    X_train_compat, y_train_compat = create_multivariate_sequences(train_price_compat, train_volume_compat, window_size)
    X_test_compat, y_test_compat = create_multivariate_sequences(test_price_compat, test_volume_compat, window_size)

    y_train_compat = np.reshape(y_train_compat, (-1,1))
    y_test_compat = np.reshape(y_test_compat, (-1,1))

    print(f'Compatibility model - X_train Shape: {X_train_compat.shape}')
    print(f'Compatibility model - y_train Shape: {y_train_compat.shape}')

    # Build and train compatibility model
    print("Training compatibility LSTM model...")

    early_stopping = EarlyStopping(monitor='val_loss', patience=20, restore_best_weights=True)
    reduce_lr = ReduceLROnPlateau(monitor='val_loss', factor=0.5, patience=10, min_lr=1e-7)

    final_model = build_advanced_lstm_model(window_size, n_features=2)
    final_model.compile(loss='mean_squared_error', optimizer=Nadam())

    final_history = final_model.fit(
        X_train_compat, y_train_compat,
        validation_data=(X_test_compat, y_test_compat),
        epochs=100,  # Reduced epochs since this is just for compatibility
        batch_size=32,
        callbacks=[early_stopping, reduce_lr],
        verbose=1
    )

    # Evaluate compatibility model
    print("\n=== Compatibility Model Evaluation ===")

    final_pred = final_model.predict(X_test_compat, verbose=0)
    final_rmse = np.sqrt(mean_squared_error(y_test_compat, final_pred))
    final_mae = mean_absolute_error(y_test_compat, final_pred)

    print(f"Compatibility Model RMSE: {final_rmse:.4f}")
    print(f"Compatibility Model MAE: {final_mae:.4f}")

    # Transform back to original scale for better interpretation
    final_pred_original = price_scaler.inverse_transform(final_pred)
    final_test_original = price_scaler.inverse_transform(y_test_compat)
    final_rmse_original = np.sqrt(mean_squared_error(final_test_original, final_pred_original))
    final_mae_original = mean_absolute_error(final_test_original, final_pred_original)

    print(f"Original Scale RMSE: ${final_rmse_original:.2f}")
    print(f"Original Scale MAE: ${final_mae_original:.2f}")

    # Use compatibility model and data for saving and visualization
    model = final_model
    history = final_history
    final_X_test = X_test_compat
    final_y_test = y_test_compat
    final_pred = final_pred

    # Save the model and scalers
    model_dir = 'AI/model'
    if not os.path.exists(model_dir):
        os.makedirs(model_dir)

    model_save_path = os.path.join(model_dir, 'bitcoin_advanced_multivariate_lstm.keras')
    model.save(model_save_path)
    print(f"Advanced Multivariate LSTM model saved to {model_save_path}")

    # Save scalers
    price_scaler_path = os.path.join(model_dir, 'bitcoin_price_scaler.save')
    joblib.dump(price_scaler, price_scaler_path)
    print(f"Price scaler saved to {price_scaler_path}")

    volume_scaler_path = os.path.join(model_dir, 'bitcoin_volume_scaler.save')
    joblib.dump(volume_scaler, volume_scaler_path)
    print(f"Volume scaler saved to {volume_scaler_path}")

    # Plot loss curves
    loss = history.history['loss']
    val_loss = history.history['val_loss']
    epochs = range(len(loss))
    plt.figure(figsize=(10, 6))
    plt.plot(epochs, loss, 'r', label='Training loss')
    plt.plot(epochs, val_loss, 'b', label='Validation loss')
    plt.title('Advanced Multivariate LSTM - Training and Validation Loss')
    plt.xlabel('Epochs')
    plt.ylabel('Loss')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.savefig(os.path.join(model_dir, 'training_loss_curves.png'), dpi=150, bbox_inches='tight')
    plt.close()

    # Final evaluation with enhanced metrics
    test_loss = model.evaluate(final_X_test, final_y_test, verbose=0)

    # Use the final prediction
    y_pred = final_pred

    MAPE = mean_absolute_percentage_error(final_y_test, y_pred)
    Accuracy = 1 - MAPE

    print(f"\n=== Final Model Performance Summary ===")
    print(f"Test Loss: {test_loss:.6f}")
    print(f"Test MAPE: {MAPE:.4f}")
    print(f"Test Accuracy: {Accuracy:.4f}")
    print(f"Final RMSE (scaled): {final_rmse:.4f}")
    print(f"Final RMSE (original): ${final_rmse_original:.2f}")
    print(f"Final MAE (original): ${final_mae_original:.2f}")

    # Enhanced visualization
    y_test_true = price_scaler.inverse_transform(final_y_test)
    y_test_pred = price_scaler.inverse_transform(y_pred)

    # Create comprehensive plots
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(20, 12))

    # Plot 1: Price prediction comparison
    split_point = int(len(price) * 0.8)
    train_dates = dates.iloc[:split_point]
    test_dates = dates.iloc[split_point:]

    train_price_original = price_scaler.inverse_transform(full_price_scaled[:split_point])
    ax1.plot(train_dates, train_price_original, color='black', lw=1, label='Training Data', alpha=0.7)
    ax1.plot(test_dates[-len(y_test_true):], y_test_true, color='blue', lw=2, label='Actual Test Data')
    ax1.plot(test_dates[-len(y_test_pred):], y_test_pred, color='red', lw=2, label='Predicted Test Data')
    ax1.set_title('Advanced Multivariate LSTM - Bitcoin Price Prediction', fontsize=14)
    ax1.set_xlabel('Date', fontsize=12)
    ax1.set_ylabel('Price ($)', fontsize=12)
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # Plot 2: Loss curves
    ax2.plot(history.history['loss'], label='Training Loss', color='blue')
    ax2.plot(history.history['val_loss'], label='Validation Loss', color='red')
    ax2.set_title('Training and Validation Loss')
    ax2.set_xlabel('Epochs')
    ax2.set_ylabel('Loss')
    ax2.legend()
    ax2.grid(True, alpha=0.3)

    # Plot 3: Prediction accuracy scatter
    ax3.scatter(y_test_true, y_test_pred, alpha=0.6, color='green')
    ax3.plot([y_test_true.min(), y_test_true.max()], [y_test_true.min(), y_test_true.max()], 'r--', lw=2)
    ax3.set_xlabel('Actual Prices ($)')
    ax3.set_ylabel('Predicted Prices ($)')
    ax3.set_title('Actual vs Predicted Prices')
    ax3.grid(True, alpha=0.3)

    # Plot 4: Residuals
    residuals = y_test_true.flatten() - y_test_pred.flatten()
    ax4.scatter(range(len(residuals)), residuals, alpha=0.6, color='purple')
    ax4.axhline(y=0, color='red', linestyle='--')
    ax4.set_xlabel('Sample Index')
    ax4.set_ylabel('Residuals ($)')
    ax4.set_title('Prediction Residuals')
    ax4.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(os.path.join(model_dir, 'advanced_lstm_analysis.png'), dpi=150, bbox_inches='tight')
    plt.close()

    # Create volume analysis plot
    plt.figure(figsize=(15, 8))

    # Plot price
    ax1 = plt.subplot(2, 1, 1)
    plt.plot(test_dates[-len(y_test_true):], y_test_true, color='blue', lw=2, label='Actual Price')
    plt.plot(test_dates[-len(y_test_pred):], y_test_pred, color='red', lw=2, label='Predicted Price')
    plt.title('Advanced Multivariate LSTM - Price Prediction with Volume Analysis')
    plt.ylabel('Price ($)')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # Plot volume
    ax2 = plt.subplot(2, 1, 2)
    test_volume_original = volume_scaler.inverse_transform(full_volume_scaled[split_point:][-len(y_test_true):])
    plt.plot(test_dates[-len(y_test_true):], test_volume_original, color='orange', lw=2, label='Volume')
    plt.ylabel('Volume')
    plt.xlabel('Date')
    plt.legend()
    plt.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(os.path.join(model_dir, 'price_volume_analysis.png'), dpi=150, bbox_inches='tight')
    plt.close()

    print(f"\nVisualization plots saved:")
    print(f"- Training loss curves: {os.path.join(model_dir, 'training_loss_curves.png')}")
    print(f"- Model analysis: {os.path.join(model_dir, 'advanced_lstm_analysis.png')}")
    print(f"- Price-volume analysis: {os.path.join(model_dir, 'price_volume_analysis.png')}")

    return model, price_scaler, volume_scaler, window_size, closedf, dates

def print_model_summary():
    """Print summary of the Enhanced VMD-LSTM-ELM Hybrid model"""
    print("\n" + "="*80)
    print("ENHANCED VMD-LSTM-ELM HYBRID BITCOIN PRICE PREDICTION MODEL")
    print("="*80)
    print("\nCore Ensemble Models:")
    print("1. ✓ Variational Mode Decomposition (VMD) for signal decomposition")
    print("2. ✓ LSTM for high-frequency components")
    print("3. ✓ Extreme Learning Machine (ELM) for medium/low-frequency components")
    print("4. ✓ Meta-Learner Neural Network for ensemble weights")
    print("5. ✓ Bayesian Neural Networks for confidence intervals")

    print("\nEnhanced Training Strategy:")
    print("- Training Set: 70% of historical data")
    print("- Validation Set: 15% for hyperparameter tuning")
    print("- Test Set: 15% for final evaluation")
    print("- Rolling Window: Expanding window approach")
    print("- Validation: Time-series cross-validation")

    print("\nTechnical Indicators:")
    print("- RSI (14 and 30-day)")
    print("- MACD (Moving Average Convergence Divergence)")
    print("- Momentum (30-day)")
    print("- Stochastic Oscillator (%D30, %D200, %K200, %K30)")
    print("- Bollinger Bands (volatility bands)")
    print("- Average True Range (ATR)")
    print("- Commodity Channel Index (CCI)")
    print("- Williams %R (momentum oscillator)")
    print("- Chaikin Money Flow (volume-weighted indicator)")

    print("\nFeature Engineering Pipeline:")
    print("- Signal Decomposition: VMD decomposition into 3-5 components")
    print("- Technical Indicators: Comprehensive technical analysis")
    print("- Lag Features: 1-10 day lagged features for key indicators")
    print("- Volatility Features: Rolling volatility windows")
    print("- Cyclical Features: Bitcoin halving cycle encoding")
    print("- Time Features: Seasonal and cyclical time patterns")

    print("\nModel Architecture:")
    print("- VMD Decomposition: K=5 modes, alpha=2000")
    print("- LSTM (High-freq): 64 units, 3 layers, dropout=0.2")
    print("- ELM (Med/Low-freq): 100 hidden units, tanh activation")
    print("- Meta-Learner: 32 hidden units, ensemble optimization")
    print("- Bayesian NN: Uncertainty quantification with confidence intervals")
    print("- Window size: 60 days")
    print("- Optimization: Adam optimizer with early stopping")
    print("="*80)

# Main execution
if __name__ == "__main__":
    print_model_summary()
    print("\nStarting Enhanced VMD-LSTM-ELM Hybrid training process...")

    # Train the model
    model, price_scaler, volume_scaler, window_size, closedf, dates = train_and_save_model()

    print("\n" + "="*80)
    print("ENHANCED TRAINING COMPLETED SUCCESSFULLY!")
    print("="*80)
    print("\nModel files saved:")
    print("- Enhanced VMD-LSTM-ELM model: AI/model/bitcoin_advanced_multivariate_lstm.keras")
    print("- Price scaler: AI/model/bitcoin_price_scaler.save")
    print("- Volume scaler: AI/model/bitcoin_volume_scaler.save")
    print("- Training loss curves: AI/model/training_loss_curves.png")
    print("- Model analysis: AI/model/advanced_lstm_analysis.png")
    print("- Price-volume analysis: AI/model/price_volume_analysis.png")

    # Generate CSV predictions
    try:
        csv_path = generate_and_save_csv_predictions(model, price_scaler, volume_scaler, closedf, window_size)
        print(f"\n✓ Predictions CSV generated: {csv_path}")
    except Exception as e:
        print(f"\n❌ Failed to generate CSV predictions: {str(e)}")
        print("Model training completed, but CSV generation failed.")

    print("\n" + "="*80)
    print("ENHANCED VMD-LSTM-ELM PROCESS COMPLETED!")
    print("="*80)
    print("\nFiles generated:")
    print("1. Enhanced model files in AI/model/")
    print("2. Predictions CSV: Data/bitcoin_predictions.csv")
    print("\nEnhanced Features Implemented:")
    print("✓ VMD signal decomposition")
    print("✓ LSTM + ELM hybrid architecture")
    print("✓ Comprehensive technical indicators")
    print("✓ Advanced feature engineering")
    print("✓ Meta-learner ensemble optimization")
    print("✓ Bayesian uncertainty quantification")
    print("✓ 70/15/15 training strategy")
    print("✓ Time-series cross-validation")
    print("\nThe enhanced system is ready for frontend use!")
    print("Run the frontend with: cd Frontend && npm start")
    print("="*80)